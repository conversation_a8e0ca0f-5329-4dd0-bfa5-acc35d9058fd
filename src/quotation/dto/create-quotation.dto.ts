import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum, IsBoolean, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateQuotationItemDto } from './create-quotation-item.dto';
import { QuotationStatus } from '../entities/quotation.entity';

export class CreateQuotationDto {
    @ApiProperty({ description: 'เลขที่ใบเสนอราคา' })
    @IsString()
    @IsNotEmpty()
    quotationNumber: string;

    @ApiProperty({ description: 'วันที่เสนอราคา' })
    @IsDateString()
    quotationDate: string;

    @ApiProperty({ description: 'วันที่หมดอายุของใบเสนอราคา', required: false })
    @IsOptional()
    @IsDateString()
    validUntil?: string;

    @ApiProperty({ description: 'สถานะ', enum: QuotationStatus, default: QuotationStatus.DRAFT })
    @IsOptional()
    @IsEnum(QuotationStatus)
    status?: QuotationStatus;

    @ApiProperty({ description: 'รหัสลูกค้า' })
    @IsNumber()
    @IsNotEmpty()
    customerId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'มีvat', default: false })
    @IsOptional()
    @IsBoolean()
    isVat?: boolean;

    @ApiProperty({ description: 'อัตราภาษี (%)', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxRate?: number;

    @ApiProperty({ description: 'จำนวนเงินภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ยอดรวม', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    subtotal?: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discount?: number;

    @ApiProperty({ description: 'ยอดรวมสุทธิ', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'วิธีการชำระเงิน' })
    @IsNumber()
    @IsNotEmpty()
    paymentMethodId: number;

    @ApiProperty({ description: 'รหัสผู้อนุมัติ', required: false })
    @IsOptional()
    @IsNumber()
    approvedById?: number;

    @ApiProperty({ description: 'วันที่อนุมัติ', required: false })
    @IsOptional()
    @IsDateString()
    approvedAt?: string;

    @ApiProperty({ description: 'รายการสินค้า', type: [CreateQuotationItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateQuotationItemDto)
    items: CreateQuotationItemDto[];
}
