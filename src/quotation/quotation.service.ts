import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { paginate, PaginateQuery, PaginateConfig, FilterOperator } from 'nestjs-paginate';
import { Quotation, QuotationStatus } from './entities/quotation.entity';
import { QuotationItem } from './entities/quotation-item.entity';
import { CreateQuotationDto } from './dto/create-quotation.dto';
import { UpdateQuotationDto } from './dto/update-quotation.dto';
import { Product } from '../product/entities/product.entity';

export const QUOTATION_PAGINATION_CONFIG: PaginateConfig<Quotation> = {
  sortableColumns: ['id', 'quotationNumber', 'quotationDate', 'status', 'totalAmount', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['quotationNumber', 'notes'],
  select: [
    'id',
    'quotationNumber', 
    'quotationDate',
    'validUntil',
    'status',
    'subtotal',
    'discount',
    'totalAmount',
    'notes',
    'terms',
    'createdAt',
    'updatedAt',
    'customer.id',
    'customer.name',
    'customer.code',
    'branch.id',
    'branch.name',
    'warehouse.id',
    'warehouse.name',
    'createdBy.id',
    'createdBy.username',
    'approvedBy.id',
    'approvedBy.username'
  ],
  relations: {
    customer: true,
    branch: true,
    warehouse: true,
    createdBy: true,
    approvedBy: true
  },
  filterableColumns: {
    status: [FilterOperator.EQ, FilterOperator.IN],
    quotationDate: [FilterOperator.GTE, FilterOperator.LTE],
    'customer.id': [FilterOperator.EQ],
    'branch.id': [FilterOperator.EQ],
    'warehouse.id': [FilterOperator.EQ],
    totalAmount: [FilterOperator.GTE, FilterOperator.LTE],
  },
  defaultLimit: 20,
  maxLimit: 100,
};

@Injectable()
export class QuotationService {
  constructor(
    @InjectRepository(Quotation)
    private quotationRepository: Repository<Quotation>,
    @InjectRepository(QuotationItem)
    private quotationItemRepository: Repository<QuotationItem>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.quotationRepository, QUOTATION_PAGINATION_CONFIG);
  }

  async generateQuotationNumber(): Promise<{ quotationNumber: string }> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    
    // หาเลขที่ใบเสนอราคาล่าสุดในเดือนนี้
    const lastQuotation = await this.quotationRepository
      .createQueryBuilder('quotation')
      .where('quotation.quotationNumber LIKE :pattern', { 
        pattern: `QT${year}${month}%` 
      })
      .orderBy('quotation.quotationNumber', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastQuotation) {
      const lastNumber = parseInt(lastQuotation.quotationNumber.slice(-4));
      nextNumber = lastNumber + 1;
    }

    const quotationNumber = `QT${year}${month}${String(nextNumber).padStart(4, '0')}`;
    
    return { quotationNumber };
  }

  async create(createQuotationDto: CreateQuotationDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // สร้าง Quotation
      const quotation = queryRunner.manager.create(Quotation, {
        ...createQuotationDto,
        quotationDate: new Date(createQuotationDto.quotationDate),
        validUntil: createQuotationDto.validUntil ? new Date(createQuotationDto.validUntil) : null,
        approvedAt: createQuotationDto.approvedAt ? new Date(createQuotationDto.approvedAt) : null,
        customer: { id: createQuotationDto.customerId } as any,
        branch: { id: createQuotationDto.branchId } as any,
        warehouse: { id: createQuotationDto.warehouseId } as any,
        createdBy: { id: userId } as any,
        approvedBy: createQuotationDto.approvedById ? { id: createQuotationDto.approvedById } as any : null,
      });

      delete (quotation as any).items;

      const savedQuotation = await queryRunner.manager.save(quotation);

      // สร้าง QuotationItems
      for (const itemDto of createQuotationDto.items) {
        const product = await Product.findOne({ 
          where: { id: itemDto.productId }, 
          relations: { unit: true } 
        });

        if (!product) {
          throw new NotFoundException(`Product with ID ${itemDto.productId} not found`);
        }

        const item = queryRunner.manager.create(QuotationItem, {
          ...itemDto,
          quotation: savedQuotation,
          product: { id: itemDto.productId } as any,
          productName: product.name,
          productCode: product.code,
          unitName: product.unit.name
        });

        await queryRunner.manager.save(item);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedQuotation.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll() {
    return this.quotationRepository.find({
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        createdBy: true,
        approvedBy: true,
        items: {
          product: {
            unit: true
          }
        },
        paymentMethod: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const quotation = await this.quotationRepository.findOne({
      where: { id },
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        createdBy: true,
        approvedBy: true,
        items: {
          product: {
            unit: true
          }
        },
        paymentMethod: true
      }
    });

    if (!quotation) {
      throw new NotFoundException(`Quotation with ID ${id} not found`);
    }

    return quotation;
  }

  async update(id: number, updateQuotationDto: UpdateQuotationDto, userId: number) {
    const quotation = await this.findOne(id);

    if (quotation.status === QuotationStatus.CONVERTED) {
      throw new BadRequestException('Cannot update converted quotation');
    }

    const updateData = this.quotationRepository.create({
      ...updateQuotationDto,
      quotationDate: updateQuotationDto.quotationDate ? new Date(updateQuotationDto.quotationDate) : undefined,
      validUntil: updateQuotationDto.validUntil ? new Date(updateQuotationDto.validUntil) : undefined,
      approvedAt: updateQuotationDto.approvedAt ? new Date(updateQuotationDto.approvedAt) : undefined,
      customer: updateQuotationDto.customerId ? { id: updateQuotationDto.customerId } as any : undefined,
      branch: updateQuotationDto.branchId ? { id: updateQuotationDto.branchId } as any : undefined,
      warehouse: updateQuotationDto.warehouseId ? { id: updateQuotationDto.warehouseId } as any : undefined,
      approvedBy: updateQuotationDto.approvedById ? { id: updateQuotationDto.approvedById } as any : undefined,
    });

    delete (updateData as any).items;

    await this.quotationRepository.update(id, updateData);

    // Update Items
    if (updateQuotationDto.items) {
      await this.quotationItemRepository.delete({ quotation: { id } });

      for (const itemDto of updateQuotationDto.items) {
        const product = await Product.findOne({ 
          where: { id: itemDto.productId }, 
          relations: { unit: true } 
        });

        if (!product) {
          throw new NotFoundException(`Product with ID ${itemDto.productId} not found`);
        }

        const item = this.quotationItemRepository.create({
          ...itemDto,
          quotation: { id } as any,
          product: { id: itemDto.productId } as any,
          productName: product.name,
          productCode: product.code,
          unitName: product.unit.name
        });

        await this.quotationItemRepository.save(item);
      }
    }

    return this.findOne(id);
  }

  async remove(id: number) {
    const quotation = await this.findOne(id);

    if (quotation.status === QuotationStatus.CONVERTED) {
      throw new BadRequestException('Cannot delete converted quotation');
    }

    await this.quotationRepository.softDelete(id);
  }

  async approve(id: number, userId: number) {
    const quotation = await this.findOne(id);

    if (quotation.status === QuotationStatus.CONVERTED) {
      throw new BadRequestException('Quotation is already converted');
    }

    await this.quotationRepository.update(id, {
      status: QuotationStatus.ACCEPTED,
      approvedBy: { id: userId } as any,
      approvedAt: new Date(),
    });

    return this.findOne(id);
  }

  async reject(id: number, userId: number) {
    const quotation = await this.findOne(id);

    if (quotation.status === QuotationStatus.CONVERTED) {
      throw new BadRequestException('Cannot reject converted quotation');
    }

    await this.quotationRepository.update(id, {
      status: QuotationStatus.REJECTED,
      approvedBy: { id: userId } as any,
      approvedAt: new Date(),
    });

    return this.findOne(id);
  }

  async markAsExpired(id: number) {
    const quotation = await this.findOne(id);

    if (quotation.status === QuotationStatus.CONVERTED) {
      throw new BadRequestException('Cannot mark converted quotation as expired');
    }

    await this.quotationRepository.update(id, {
      status: QuotationStatus.EXPIRED,
    });

    return this.findOne(id);
  }

  async markAsConverted(id: number, saleId: number) {
    await this.quotationRepository.update(id, {
      status: QuotationStatus.CONVERTED,
      convertedSaleId: saleId,
      convertedAt: new Date(),
    });

    return this.findOne(id);
  }
}
