import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { paginate, PaginateQuery, PaginateConfig, FilterOperator } from 'nestjs-paginate';
import { DeliveryNote, DeliveryNoteStatus } from './entities/delivery-note.entity';
import { DeliveryNoteItem } from './entities/delivery-note-item.entity';
import { CreateDeliveryNoteDto } from './dto/create-delivery-note.dto';
import { UpdateDeliveryNoteDto } from './dto/update-delivery-note.dto';
import { Product } from '../product/entities/product.entity';
import { Sale } from '../sale/entities/sale.entity';
import { SaleItem } from '../sale/entities/sale-item.entity';

export const DELIVERY_NOTE_PAGINATION_CONFIG: PaginateConfig<DeliveryNote> = {
  sortableColumns: ['id', 'deliveryNoteNumber', 'deliveryDate', 'status', 'totalQuantity', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['deliveryNoteNumber', 'notes', 'contactPerson'],
  select: [
    'id',
    'deliveryNoteNumber', 
    'deliveryDate',
    'expectedDeliveryDate',
    'actualDeliveryDate',
    'status',
    'deliveryAddress',
    'contactPerson',
    'contactPhone',
    'totalQuantity',
    'totalWeight',
    'vehicleNumber',
    'driverName',
    'notes',
    'createdAt',
    'updatedAt',
    'sale.id',
    'sale.saleNumber',
    'customer.id',
    'customer.name',
    'customer.code',
    'branch.id',
    'branch.name',
    'warehouse.id',
    'warehouse.name',
    'createdBy.id',
    'createdBy.username',
    'deliveredBy.id',
    'deliveredBy.username',
    'receivedBy.id',
    'receivedBy.username'
  ],
  relations: {
    sale: true,
    customer: true,
    branch: true,
    warehouse: true,
    createdBy: true,
    deliveredBy: true,
    receivedBy: true
  },
  filterableColumns: {
    status: [FilterOperator.EQ, FilterOperator.IN],
    deliveryDate: [FilterOperator.GTE, FilterOperator.LTE],
    'sale.id': [FilterOperator.EQ],
    'customer.id': [FilterOperator.EQ],
    'branch.id': [FilterOperator.EQ],
    'warehouse.id': [FilterOperator.EQ],
  },
  defaultLimit: 20,
  maxLimit: 100,
};

@Injectable()
export class DeliveryNoteService {
  constructor(
    @InjectRepository(DeliveryNote)
    private deliveryNoteRepository: Repository<DeliveryNote>,
    @InjectRepository(DeliveryNoteItem)
    private deliveryNoteItemRepository: Repository<DeliveryNoteItem>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.deliveryNoteRepository, DELIVERY_NOTE_PAGINATION_CONFIG);
  }

  async generateDeliveryNoteNumber(): Promise<{ deliveryNoteNumber: string }> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    
    // หาเลขที่ใบส่งของล่าสุดในเดือนนี้
    const lastDeliveryNote = await this.deliveryNoteRepository
      .createQueryBuilder('deliveryNote')
      .where('deliveryNote.deliveryNoteNumber LIKE :pattern', { 
        pattern: `DN${year}${month}%` 
      })
      .orderBy('deliveryNote.deliveryNoteNumber', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastDeliveryNote) {
      const lastNumber = parseInt(lastDeliveryNote.deliveryNoteNumber.slice(-4));
      nextNumber = lastNumber + 1;
    }

    const deliveryNoteNumber = `DN${year}${month}${String(nextNumber).padStart(4, '0')}`;
    
    return { deliveryNoteNumber };
  }

  async createFromSale(saleId: number, userId: number) {
    const sale = await this.dataSource.getRepository(Sale).findOne({
      where: { id: saleId },
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        items: {
          product: {
            unit: true
          }
        }
      }
    });

    if (!sale) {
      throw new NotFoundException(`Sale with ID ${saleId} not found`);
    }

    const { deliveryNoteNumber } = await this.generateDeliveryNoteNumber();

    const createDeliveryNoteDto: CreateDeliveryNoteDto = {
      deliveryNoteNumber,
      deliveryDate: new Date().toISOString(),
      saleId: sale.id,
      customerId: sale.customer.id,
      branchId: sale.branch.id,
      warehouseId: sale.warehouse.id,
      deliveryAddress: sale.customer.address || '',
      contactPerson: sale.customer.name,
      contactPhone: sale.customer.phoneNumber || '',
      totalQuantity: sale.items.reduce((sum, item) => sum + item.quantity, 0),
      items: sale.items.map(saleItem => ({
        saleItemId: saleItem.id,
        productId: saleItem.product.id,
        orderedQuantity: saleItem.quantity,
        deliveredQuantity: saleItem.quantity, // เริ่มต้นให้เท่ากับที่สั่ง
      }))
    };

    return this.create(createDeliveryNoteDto, userId);
  }

  async create(createDeliveryNoteDto: CreateDeliveryNoteDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // สร้าง DeliveryNote
      const deliveryNote = queryRunner.manager.create(DeliveryNote, {
        ...createDeliveryNoteDto,
        deliveryDate: new Date(createDeliveryNoteDto.deliveryDate),
        expectedDeliveryDate: createDeliveryNoteDto.expectedDeliveryDate ? new Date(createDeliveryNoteDto.expectedDeliveryDate) : null,
        actualDeliveryDate: createDeliveryNoteDto.actualDeliveryDate ? new Date(createDeliveryNoteDto.actualDeliveryDate) : null,
        deliveredAt: createDeliveryNoteDto.deliveredAt ? new Date(createDeliveryNoteDto.deliveredAt) : null,
        receivedAt: createDeliveryNoteDto.receivedAt ? new Date(createDeliveryNoteDto.receivedAt) : null,
        sale: { id: createDeliveryNoteDto.saleId } as any,
        customer: { id: createDeliveryNoteDto.customerId } as any,
        branch: { id: createDeliveryNoteDto.branchId } as any,
        warehouse: { id: createDeliveryNoteDto.warehouseId } as any,
        createdBy: { id: userId } as any,
        deliveredBy: createDeliveryNoteDto.deliveredById ? { id: createDeliveryNoteDto.deliveredById } as any : null,
        receivedBy: createDeliveryNoteDto.receivedById ? { id: createDeliveryNoteDto.receivedById } as any : null,
      });

      delete (deliveryNote as any).items;

      const savedDeliveryNote = await queryRunner.manager.save(deliveryNote);

      // สร้าง DeliveryNoteItems
      for (const itemDto of createDeliveryNoteDto.items) {
        const product = await Product.findOne({ 
          where: { id: itemDto.productId }, 
          relations: { unit: true } 
        });

        if (!product) {
          throw new NotFoundException(`Product with ID ${itemDto.productId} not found`);
        }

        const item = queryRunner.manager.create(DeliveryNoteItem, {
          ...itemDto,
          // expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          deliveryNote: savedDeliveryNote,
          saleItem: itemDto.saleItemId ? { id: itemDto.saleItemId } as any : null,
          product: { id: itemDto.productId } as any,
          productName: product.name,
          productCode: product.code,
          unitName: product.unit.name
        });

        await queryRunner.manager.save(item);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedDeliveryNote.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll() {
    return this.deliveryNoteRepository.find({
      relations: {
        sale: true,
        customer: true,
        branch: true,
        warehouse: true,
        createdBy: true,
        deliveredBy: true,
        receivedBy: true,
        items: {
          saleItem: true,
          product: {
            unit: true
          }
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const deliveryNote = await this.deliveryNoteRepository.findOne({
      where: { id },
      relations: {
        sale: true,
        customer: true,
        branch: true,
        warehouse: true,
        createdBy: true,
        deliveredBy: true,
        receivedBy: true,
        items: {
          saleItem: true,
          product: {
            unit: true
          }
        }
      }
    });

    if (!deliveryNote) {
      throw new NotFoundException(`DeliveryNote with ID ${id} not found`);
    }

    return deliveryNote;
  }

  async update(id: number, updateDeliveryNoteDto: UpdateDeliveryNoteDto, userId: number) {
    const deliveryNote = await this.findOne(id);

    if (deliveryNote.status === DeliveryNoteStatus.DELIVERED) {
      throw new BadRequestException('Cannot update delivered delivery note');
    }

    const updateData = this.deliveryNoteRepository.create({
      ...updateDeliveryNoteDto,
      deliveryDate: updateDeliveryNoteDto.deliveryDate ? new Date(updateDeliveryNoteDto.deliveryDate) : undefined,
      expectedDeliveryDate: updateDeliveryNoteDto.expectedDeliveryDate ? new Date(updateDeliveryNoteDto.expectedDeliveryDate) : undefined,
      actualDeliveryDate: updateDeliveryNoteDto.actualDeliveryDate ? new Date(updateDeliveryNoteDto.actualDeliveryDate) : undefined,
      deliveredAt: updateDeliveryNoteDto.deliveredAt ? new Date(updateDeliveryNoteDto.deliveredAt) : undefined,
      receivedAt: updateDeliveryNoteDto.receivedAt ? new Date(updateDeliveryNoteDto.receivedAt) : undefined,
      sale: updateDeliveryNoteDto.saleId ? { id: updateDeliveryNoteDto.saleId } as any : undefined,
      customer: updateDeliveryNoteDto.customerId ? { id: updateDeliveryNoteDto.customerId } as any : undefined,
      branch: updateDeliveryNoteDto.branchId ? { id: updateDeliveryNoteDto.branchId } as any : undefined,
      warehouse: updateDeliveryNoteDto.warehouseId ? { id: updateDeliveryNoteDto.warehouseId } as any : undefined,
      deliveredBy: updateDeliveryNoteDto.deliveredById ? { id: updateDeliveryNoteDto.deliveredById } as any : undefined,
      receivedBy: updateDeliveryNoteDto.receivedById ? { id: updateDeliveryNoteDto.receivedById } as any : undefined,
    });

    delete (updateData as any).items;

    await this.deliveryNoteRepository.update(id, updateData);

    // Update Items
    if (updateDeliveryNoteDto.items) {
      await this.deliveryNoteItemRepository.delete({ deliveryNote: { id } });

      for (const itemDto of updateDeliveryNoteDto.items) {
        const product = await Product.findOne({ 
          where: { id: itemDto.productId }, 
          relations: { unit: true } 
        });

        if (!product) {
          throw new NotFoundException(`Product with ID ${itemDto.productId} not found`);
        }

        const item = this.deliveryNoteItemRepository.create({
          ...itemDto,
          // expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          deliveryNote: { id } as any,
          saleItem: itemDto.saleItemId ? { id: itemDto.saleItemId } as any : null,
          product: { id: itemDto.productId } as any,
          productName: product.name,
          productCode: product.code,
          unitName: product.unit.name
        });

        await this.deliveryNoteItemRepository.save(item);
      }
    }

    return this.findOne(id);
  }

  async remove(id: number) {
    const deliveryNote = await this.findOne(id);

    if (deliveryNote.status === DeliveryNoteStatus.DELIVERED) {
      throw new BadRequestException('Cannot delete delivered delivery note');
    }

    await this.deliveryNoteRepository.softDelete(id);
  }

  async updateStatus(id: number, status: DeliveryNoteStatus, userId: number) {
    const deliveryNote = await this.findOne(id);

    const updateData: any = { status };

    if (status === DeliveryNoteStatus.DELIVERED) {
      updateData.deliveredBy = { id: userId };
      updateData.deliveredAt = new Date();
      updateData.actualDeliveryDate = new Date();
    }

    await this.deliveryNoteRepository.update(id, updateData);

    return this.findOne(id);
  }

  async markAsDelivered(id: number, userId: number, receiverName?: string, receiverSignature?: string) {
    const updateData: any = {
      status: DeliveryNoteStatus.DELIVERED,
      deliveredBy: { id: userId },
      deliveredAt: new Date(),
      actualDeliveryDate: new Date(),
    };

    if (receiverName) {
      updateData.receiverName = receiverName;
    }

    if (receiverSignature) {
      updateData.receiverSignature = receiverSignature;
    }

    await this.deliveryNoteRepository.update(id, updateData);

    return this.findOne(id);
  }
}
